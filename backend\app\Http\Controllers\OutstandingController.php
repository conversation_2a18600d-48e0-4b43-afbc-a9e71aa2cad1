<?php

namespace App\Http\Controllers;

use App\Models\Sale;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\ChequeStatement;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\SalesReturn;
use App\Models\SalesReturnItem;

class OutstandingController extends Controller
{
    /**
     * Helper: Get total approved sales return amount for a sale or invoice (credit/cheque only)
     */
    private function getApprovedSalesReturnAmount($type, $reference, $paymentType)
    {
        if (!in_array(strtolower($paymentType), ['credit', 'cheque'])) {
            return 0;
        }
        if ($type === 'sale') {
            $returns = SalesReturn::where('bill_number', $reference)
                ->where('status', 'approved')
                ->get();
        } elseif ($type === 'invoice') {
            $returns = SalesReturn::where('invoice_no', $reference)
                ->where('status', 'approved')
                ->get();
        } else {
            return 0;
        }
        $returnAmount = 0;
        foreach ($returns as $return) {
            $returnAmount += SalesReturnItem::where('sales_return_id', $return->id)
                ->get()
                ->sum(function($item) {
                    return $item->quantity * $item->selling_cost;
                });
        }
        return $returnAmount;
    }

    public function index(Request $request)
    {
        try {
            $customerId = $request->query('customer_id');

            $salesQuery = Sale::select('id', 'customer_id', 'customer_name', 'bill_number', 'total', 'received_amount', 'created_at', 'payment_type');

            if ($customerId) {
                $salesQuery->where('customer_id', $customerId);
            }

            $sales = $salesQuery->get()
                ->map(function ($sale) {
                    $pendingAmount = (float) $sale->total - (float) $sale->received_amount;

                    // Subtract approved sales return amount for this sale (if credit/cheque)
                    $salesReturnAmount = $this->getApprovedSalesReturnAmount('sale', $sale->bill_number, $sale->payment_type);
                    $pendingAmount -= $salesReturnAmount;
                    if ($pendingAmount < 0) $pendingAmount = 0;

                    $paymentHistory = Payment::where('transaction_type', 'sale')
                        ->where('transaction_id', $sale->id)
                        ->orderBy('payment_date', 'asc')
                        ->get(['amount', 'payment_date', 'payment_method', 'cheque_no', 'bank_name', 'issue_date', 'note', 'reference_no'])
                        ->map(function ($payment) {
                            return [
                                'amount' => (float) $payment->amount,
                                'payment_date' => $payment->payment_date ? $payment->payment_date->toDateString() : null,
                                'payment_method' => $payment->payment_method,
                                'cheque_no' => $payment->cheque_no,
                                'bank_name' => $payment->bank_name,
                                'issue_date' => $payment->issue_date ? $payment->issue_date->toDateString() : null,
                                'note' => $payment->note,
                                'reference_no' => $payment->reference_no,
                            ];
                        });

                    return [
                        'id' => $sale->id,
                        'type' => 'sale',
                        'customer_id' => $sale->customer_id,
                        'customer_name' => $sale->customer_name ?: 'Unknown Customer',
                        'bill_number' => $sale->bill_number,
                        'reference_no' => $sale->bill_number,
                        'total_amount' => (float) $sale->total,
                        'final_outstanding_amount' => $pendingAmount,
                        'paid_amount' => (float) $sale->received_amount,
                        'sales_return_amount' => $salesReturnAmount,
                        'payment_history' => $paymentHistory,
                        'previous_outstanding_balance' => 0.0,
                        'total_credits' => 0.0,
                        'date' => $sale->created_at ? $sale->created_at->toDateString() : null,
                        'payment_type' => $sale->payment_type,
                        'status' => $pendingAmount > 0 ? ($pendingAmount == $sale->total ? 'Pending' : 'Partial') : 'Paid',
                    ];
                })->filter(function ($sale) {
                    return $sale['final_outstanding_amount'] > 0;
                });

            $invoicesQuery = Invoice::select('id', 'customer_id', 'customer_name', 'invoice_no', 'total_amount', 'purchase_amount', 'invoice_date', 'payment_method');

            if ($customerId) {
                $invoicesQuery->where('customer_id', $customerId);
            }

            $invoices = $invoicesQuery->get()
                ->map(function ($invoice) {
                    $pendingAmount = (float) $invoice->total_amount - (float) $invoice->purchase_amount;

                    // Subtract approved sales return amount for this invoice (if credit/cheque)
                    $salesReturnAmount = $this->getApprovedSalesReturnAmount('invoice', $invoice->invoice_no, $invoice->payment_method);
                    $pendingAmount -= $salesReturnAmount;
                    if ($pendingAmount < 0) $pendingAmount = 0;

                    $paymentHistory = Payment::where('transaction_type', 'invoice')
                        ->where('transaction_id', $invoice->id)
                        ->orderBy('payment_date', 'asc')
                        ->get(['amount', 'payment_date', 'payment_method', 'cheque_no', 'bank_name', 'issue_date', 'note', 'reference_no'])
                        ->map(function ($payment) {
                            return [
                                'amount' => (float) $payment->amount,
                                'payment_date' => $payment->payment_date ? $payment->payment_date->toDateString() : null,
                                'payment_method' => $payment->payment_method,
                                'cheque_no' => $payment->cheque_no,
                                'bank_name' => $payment->bank_name,
                                'issue_date' => $payment->issue_date ? $payment->issue_date->toDateString() : null,
                                'note' => $payment->note,
                                'reference_no' => $payment->reference_no,
                            ];
                        });

                    return [
                        'id' => $invoice->id,
                        'type' => 'invoice',
                        'customer_id' => $invoice->customer_id ?: $invoice->customer_name,
                        'customer_name' => $invoice->customer_name ?: 'Unknown Customer',
                        'invoice_no' => $invoice->invoice_no,
                        'reference_no' => $invoice->invoice_no,
                        'total_amount' => (float) $invoice->total_amount,
                        'final_outstanding_amount' => $pendingAmount,
                        'paid_amount' => (float) $invoice->purchase_amount,
                        'sales_return_amount' => $salesReturnAmount,
                        'payment_history' => $paymentHistory,
                        'previous_outstanding_balance' => 0.0,
                        'total_credits' => 0.0,
                        'date' => $invoice->invoice_date ? $invoice->invoice_date->toDateString() : null,
                        'payment_type' => $invoice->payment_method,
                        'status' => $pendingAmount > 0 ? ($pendingAmount == $invoice->total_amount ? 'Pending' : 'Partial') : 'Paid',
                    ];
                })->filter(function ($invoice) {
                    return $invoice['final_outstanding_amount'] > 0;
                });

            $outstanding = collect($sales)->merge(collect($invoices));

            // Add customer's opening balance as a transaction if customer_id is provided
            if ($customerId) {
                $customer = Customer::find($customerId);
                if ($customer && $customer->openingbalance > 0) {
                    // Calculate settled opening balance for this customer
                    $settledOpeningBalance = Payment::where('refer_type', 'Customer')
                        ->where('refer_id', $customerId)
                        ->where('transaction_type', 'opening_balance')
                        ->sum('opening_balance');

                    $remainingOpeningBalance = (float) $customer->openingbalance - (float) $settledOpeningBalance;

                    if ($remainingOpeningBalance > 0) {
                        $outstanding->push([
                            'id' => 'opening_balance_' . $customerId,
                            'type' => 'opening_balance',
                            'customer_id' => $customer->id,
                            'customer_name' => $customer->customer_name,
                            'bill_number' => null,
                            'reference_no' => 'OPENING-' . $customerId,
                            'total_amount' => (float) $customer->openingbalance,
                            'final_outstanding_amount' => $remainingOpeningBalance,
                            'paid_amount' => (float) $settledOpeningBalance,
                            'payment_history' => [],
                            'previous_outstanding_balance' => 0.0,
                            'total_credits' => 0.0,
                            'date' => null,
                            'payment_type' => null,
                            'status' => 'Pending',
                        ]);
                    }
                }
            }

            $outstanding = $outstanding->sortByDesc('date')->values();

            // Calculate total pending amount
            $totalPendingAmount = $outstanding->sum('final_outstanding_amount');

            return response()->json([
                'transactions' => $outstanding->toArray(),
                'total_pending_amount' => $totalPendingAmount,
                'customer_opening_balance' => $customerId && $customer ? (float) $customer->openingbalance : 0.0,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching outstanding transactions: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json(['error' => 'Failed to fetch outstanding transactions.'], 500);
        }
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'paid_amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|string|in:Cash,Card,Cheque,Online Payment',
            'cheque_no' => 'nullable|string|required_if:payment_method,Cheque',
            'bank_name' => 'nullable|string|required_if:payment_method,Cheque',
            'bank' => 'nullable|string',
            'issue_date' => 'nullable|date|required_if:payment_method,Cheque',
            'status' => 'required|string|in:Paid,Partial,Pending',
            'final_outstanding_amount' => 'required|numeric|min:0',
            'note' => 'nullable|string',
            'reference_no' => 'nullable|string',
            'transaction_type' => 'required|string|in:sale,invoice,opening_balance',
            'opening_balance' => 'nullable|numeric|min:0',
            'customer_id' => 'required|exists:customers,id',
            'discount' => 'nullable|numeric|min:0',
        ]);

        DB::beginTransaction();
        try {
            // Soft delete a payment (receive voucher) if requested
            if ($request->has('delete') && $request->boolean('delete') && $request->has('payment_id')) {
                $payment = Payment::find($request->input('payment_id'));
                if ($payment) {
                    $payment->deleted_by = $request->user()->id ?? auth()->id();
                    $payment->save();
                    $payment->delete();
                    DB::commit();
                    return response()->json(['message' => 'Receive voucher deleted successfully.'], 200);
                } else {
                    DB::rollBack();
                    return response()->json(['error' => 'Payment not found.'], 404);
                }
            }
            $transactionType = $request->input('transaction_type');
            $paidAmount = (float) $request->input('paid_amount');
            $paymentDate = $request->input('payment_date');
            $paymentMethod = $request->input('payment_method');
            $chequeNo = $request->input('cheque_no');
            $bankName = $request->input('bank_name');
            $issueDate = $request->input('issue_date');
            $status = $request->input('status');
            $note = $request->input('note');
            $referenceNo = $request->input('reference_no');
            $openingBalance = (float) ($request->input('opening_balance') ?? 0);
            $customerId = $request->input('customer_id');
            $discount = (float) ($request->input('discount') ?? 0);

            $customer = Customer::find($customerId);
            if (!$customer) {
                return response()->json(['error' => 'Customer not found.'], 404);
            }

            // Get bank ledger name if bank account is selected
            $bankLedgerName = null;
            if (in_array($paymentMethod, ['Card', 'Cheque', 'Online Payment']) && !empty($request->input('bank'))) {
                $bankAccountInfo = $request->input('bank');
                $bankAccountParts = explode('-', $bankAccountInfo);
                
                if (count($bankAccountParts) === 2) {
                    $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
                    $bankAccountId = $bankAccountParts[1];
                    
                    if ($bankAccountType === 'staff_ledger') {
                        $staffLedger = \App\Models\StaffLedger::find($bankAccountId);
                        $bankLedgerName = $staffLedger ? $staffLedger->name : null;
                    } else {
                        $subGroup = \App\Models\AccountSubGroup::find($bankAccountId);
                        $bankLedgerName = $subGroup ? $subGroup->sub_group_name : null;
                    }
                }
            }

            // Prepare payment data
            $paymentData = [
                'voucher_no' => Payment::getNextReceiveVoucherNumber(),
                'transaction_type' => $transactionType,
                'reference_no' => $referenceNo,
                'amount' => $paidAmount,
                'discount' => $discount,
                'payment_date' => $paymentDate,
                'payment_method' => $paymentMethod,
                'cheque_no' => $paymentMethod === 'Cheque' ? $chequeNo : null,
                'bank_name' => $paymentMethod === 'Cheque' ? $bankName : null,
                'bank' => $bankLedgerName,
                'issue_date' => $paymentMethod === 'Cheque' ? $issueDate : null,
                'note' => $note,
                'refer_type' => 'Customer',
                'refer_id' => $customerId,
                'refer_name' => $customer->customer_name,
            ];

            if ($transactionType === 'sale') {
                $sale = Sale::find($id);
                if (!$sale) {
                    return response()->json(['error' => 'Sale not found.'], 404);
                }

                $total = (float) $sale->total;
                $currentPaid = (float) $sale->received_amount;
                $newPaid = $currentPaid + $paidAmount;

                if ($newPaid > $total) {
                    return response()->json(['error' => 'Payment amount exceeds total amount.'], 422);
                }

                $saleStatus = 'pending';
                if ($newPaid >= $total) {
                    $saleStatus = 'paid';
                } elseif ($newPaid > 0) {
                    $saleStatus = 'partial';
                }

                $sale->received_amount = $newPaid;
                $sale->balance_amount = $total - $newPaid;
                $sale->payment_type = $paymentMethod;
                $sale->status = $saleStatus;
                $sale->updated_at = now();
                $sale->save();

                $paymentData['transaction_id'] = $sale->id;
                $payment = Payment::create($paymentData);

                // Log user activity for receive voucher creation
                $userId = auth()->id();
                if ($userId) {
                    \App\Http\Controllers\UserActivityController::logActivity(
                        $userId,
                        'Receive Voucher Created',
                        'Receive Vouchers',
                        $payment->id,
                        [
                            'voucher_no' => $payment->voucher_no,
                            'transaction_type' => $payment->transaction_type,
                            'amount' => $payment->amount,
                            'payment_method' => $payment->payment_method,
                            'customer_id' => $customerId,
                            'customer_name' => $customer->customer_name
                        ]
                    );
                } else {
                    \Log::error('Cannot log Receive Voucher Created - no authenticated user found');
                }

                if (strtolower($paymentMethod) === 'cheque') {
                    ChequeStatement::create([
                        'payment_id' => $payment->id,
                        'voucher_no' => $payment->voucher_no,
                        'transaction_id' => $payment->transaction_id,
                        'transaction_type' => $payment->transaction_type,
                        'reference_no' => $payment->reference_no,
                        'refer_type' => $payment->refer_type,
                        'refer_id' => $payment->refer_id,
                        'refer_name' => $payment->refer_name,
                        'amount' => $payment->amount,
                        'payment_date' => $payment->payment_date,
                        'cheque_no' => $payment->cheque_no,
                        'bank_name' => $payment->bank_name,
                        'issue_date' => $payment->issue_date,
                        'note' => $payment->note,
                        'status' => 'pending',
                    ]);
                }

                DB::commit();
                return response()->json([
                    'message' => 'Sale payment updated successfully.',
                    'status' => $saleStatus,
                    'balance' => $sale->balance_amount,
                ]);
            }

            if ($transactionType === 'invoice') {
                $invoice = Invoice::find($id);
                if (!$invoice) {
                    return response()->json(['error' => 'Invoice not found.'], 404);
                }

                $total = (float) $invoice->total_amount;
                $currentPaid = (float) $invoice->purchase_amount;
                $newPaid = $currentPaid + $paidAmount;

                if ($newPaid > $total) {
                    return response()->json(['error' => 'Payment amount exceeds total amount.'], 422);
                }

                $invoice->purchase_amount = $newPaid;
                $invoice->balance = $total - $newPaid;
                $invoice->status = $status;
                $invoice->payment_method = $paymentMethod;
                $invoice->updated_at = now();
                $invoice->save();

                $paymentData['transaction_id'] = $invoice->id;
                $payment = Payment::create($paymentData);

                // Log user activity for receive voucher creation
                $userId = auth()->id();
                if ($userId) {
                    \App\Http\Controllers\UserActivityController::logActivity(
                        $userId,
                        'Receive Voucher Created',
                        'Receive Vouchers',
                        $payment->id,
                        [
                            'voucher_no' => $payment->voucher_no,
                            'transaction_type' => $payment->transaction_type,
                            'amount' => $payment->amount,
                            'payment_method' => $payment->payment_method,
                            'customer_id' => $customerId,
                            'customer_name' => $customer->customer_name
                        ]
                    );
                } else {
                    \Log::error('Cannot log Receive Voucher Created - no authenticated user found');
                }

                if (strtolower($paymentMethod) === 'cheque') {
                    ChequeStatement::create([
                        'payment_id' => $payment->id,
                        'voucher_no' => $payment->voucher_no,
                        'transaction_id' => $payment->transaction_id,
                        'transaction_type' => $payment->transaction_type,
                        'reference_no' => $payment->reference_no,
                        'refer_type' => $payment->refer_type,
                        'refer_id' => $payment->refer_id,
                        'refer_name' => $payment->refer_name,
                        'amount' => $payment->amount,
                        'payment_date' => $payment->payment_date,
                        'cheque_no' => $payment->cheque_no,
                        'bank_name' => $payment->bank_name,
                        'issue_date' => $payment->issue_date,
                        'note' => $payment->note,
                        'status' => 'pending',
                    ]);
                }

                DB::commit();
                return response()->json([
                    'message' => 'Invoice payment updated successfully.',
                    'status' => $invoice->status,
                    'balance' => $invoice->balance,
                ]);
            }

            if ($transactionType === 'opening_balance') {
                // Validate that opening balance payment doesn't exceed remaining opening balance
                $settledOpeningBalance = Payment::where('refer_type', 'Customer')
                    ->where('refer_id', $customerId)
                    ->where('transaction_type', 'opening_balance')
                    ->sum('opening_balance');

                $remainingOpeningBalance = (float) $customer->openingbalance - (float) $settledOpeningBalance;
                if ($openingBalance > $remainingOpeningBalance) {
                    return response()->json(['error' => 'Opening balance payment exceeds remaining opening balance.'], 400);
                }

                // Set transaction_id to null for opening_balance
                $paymentData['transaction_id'] = null;
                $paymentData['opening_balance'] = $openingBalance;
                $payment = Payment::create($paymentData);

                // Log user activity for receive voucher creation
                $userId = auth()->id();
                if ($userId) {
                    \App\Http\Controllers\UserActivityController::logActivity(
                        $userId,
                        'Receive Voucher Created',
                        'Receive Vouchers',
                        $payment->id,
                        [
                            'voucher_no' => $payment->voucher_no,
                            'transaction_type' => $payment->transaction_type,
                            'amount' => $payment->amount,
                            'payment_method' => $payment->payment_method,
                            'customer_id' => $customerId,
                            'customer_name' => $customer->customer_name,
                            'opening_balance' => $openingBalance
                        ]
                    );
                } else {
                    \Log::error('Cannot log Receive Voucher Created - no authenticated user found');
                }

                if (strtolower($paymentMethod) === 'cheque') {
                    // Ensure transaction_id is nullable for ChequeStatement
                    ChequeStatement::create([
                        'payment_id' => $payment->id,
                        'voucher_no' => $payment->voucher_no,
                        'transaction_id' => null, // Explicitly set to null for opening_balance
                        'transaction_type' => $payment->transaction_type,
                        'reference_no' => $payment->reference_no,
                        'refer_type' => $payment->refer_type,
                        'refer_id' => $payment->refer_id,
                        'refer_name' => $payment->refer_name,
                        'amount' => $payment->amount,
                        'payment_date' => $payment->payment_date,
                        'cheque_no' => $payment->cheque_no,
                        'bank_name' => $payment->bank_name,
                        'issue_date' => $payment->issue_date,
                        'note' => $payment->note,
                        'status' => 'pending',
                    ]);
                }

                DB::commit();
                return response()->json([
                    'message' => 'Opening balance payment processed successfully.',
                    'status' => $remainingOpeningBalance - $openingBalance <= 0 ? 'Paid' : 'Pending',
                    'balance' => $remainingOpeningBalance - $openingBalance,
                ]);
            }

            return response()->json(['error' => 'Invalid transaction type.'], 400);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating transaction: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json([
                'error' => 'Failed to update transaction.',
                'details' => $e->getMessage(),
            ], 500);
        }
    }

    public function deleted()
    {
        $vouchers = Payment::onlyTrashed()->with('deletedByUser')->where('voucher_no', 'like', 'REC-%')->orderByDesc('id')->get();
        return response()->json(['success' => true, 'data' => $vouchers->map(function($voucher) {
            return array_merge($voucher->toArray(), [
                'deleted_by_user' => $voucher->deletedByUser ? [
                    'id' => $voucher->deletedByUser->id,
                    'name' => $voucher->deletedByUser->name,
                    'email' => $voucher->deletedByUser->email,
                ] : null,
            ]);
        })]);
    }
}