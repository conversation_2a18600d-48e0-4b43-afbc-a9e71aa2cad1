<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Product;
use App\Models\PaymentMethod;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Throwable;
use App\Models\User;
use App\Models\Payment;
use App\Models\ChequeStatement;

class SalesInvoiceController extends Controller
{
    public function index(): JsonResponse
    {
        // Eager load deletedByUser and approvedByUser for frontend use
        return response()->json(Invoice::with(['items.productVariant', 'deletedByUser', 'approvedByUser'])->latest()->paginate(15));
    }

    public function store(): JsonResponse
    {
        Log::info('Store Invoice Request Data:', request()->all());
        Log::info('Store Invoice Approved By in Request', ['approved_by' => request()->input('approved_by')]);

        $validator = Validator::make(request()->all(), [
            'invoice.no' => 'nullable|string|max:255|unique:invoices,invoice_no',
            'invoice.date' => 'nullable|date',
            'invoice.time' => 'nullable|date_format:H:i',
            'customer.id' => 'nullable|exists:customers,id',
            'customer.name' => 'nullable|string|max:255',
            'customer.address' => 'nullable|string|max:255',
            'customer.phone' => 'nullable|string|max:20',
            'customer.email' => 'nullable|email|max:255',
            'purchaseDetails.method' => 'nullable|string|in:cash,card,bank_transfer,cheque,online,credit',
            'purchaseDetails.amount' => 'nullable|numeric|min:0',
            'purchaseDetails.taxPercentage' => 'nullable|numeric|min:0|max:100',
            'items' => 'nullable|array|min:1',
            'items.*.product_id' => 'nullable|exists:products,product_id',
            'items.*.variant_id' => 'nullable|integer', // Batch tracking
            'items.*.batch_number' => 'nullable|string|max:255', // Batch tracking
            'items.*.expiry_date' => 'nullable|string', // Batch tracking
            'items.*.description' => 'nullable|string|max:255',
            'items.*.qty' => 'required|numeric|min:0.01',
            'items.*.free' => 'nullable|numeric|min:0',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.sales_price' => 'nullable|numeric|min:0',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.special_discount' => 'nullable|numeric|min:0',
            'items.*.total_buying_cost' => 'nullable|numeric|min:0',
            'items.*.profit' => 'nullable|numeric|min:0',
            'status' => 'nullable|string|in:pending,paid,cancelled,unpaid,approved,rejected,partial',
            'items.*.supplier' => 'nullable|string|max:255',
            'items.*.category' => 'nullable|string|max:255',
            'items.*.store_location' => 'nullable|string|max:255',
            'quotation_id' => 'nullable|exists:quotations,id',
            'prepared_by' => 'nullable|string|max:255',
            'approved_by' => 'nullable|string|max:255',
            'cheque_no' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'bank' => 'nullable|string|max:255',
            'issue_date' => 'nullable|date',
        ]);

        // Cheque validation
        $input = request()->all();
        if (($input['purchaseDetails']['method'] ?? null) === 'cheque') {
            $validator->addRules([
                'cheque_no' => 'required|string|max:255',
                'bank_name' => 'required|string|max:255',
                'issue_date' => 'required|date',
            ]);
        }

        if ($validator->fails()) {
            Log::warning('Validation failed for invoice store:', [
                'errors' => $validator->errors(),
                'request_data' => request()->all(),
            ]);
            return response()->json([
                'message' => 'Validation failed.',
                'errors' => $validator->errors(),
            ], 422);
        }

        $validatedData = $validator->validated();
        $input = request()->all();
        $redeemPoints = 0;
        if (isset($input['redeem_points'])) {
            $redeemPoints = floatval($input['redeem_points']);
        } elseif (isset($input['redeem'])) {
            $redeemPoints = floatval($input['redeem']);
        }
        $totalPaid = floatval($validatedData['purchaseDetails']['amount'] ?? 0) + floatval($redeemPoints ?? 0);
        Log::info('Validated Store Invoice Data:', [
            'approved_by' => $validatedData['approved_by'] ?? 'not set',
            'full_data' => $validatedData,
         ]);
        // Save approved_by as is or null if not set, no default
        $approvedBy = $validatedData['approved_by'] ?? null;

        // Calculate totals
        Log::info('Calculating invoice totals...');
        
        $taxRate = isset($validatedData['purchaseDetails']['taxPercentage'])
            ? $validatedData['purchaseDetails']['taxPercentage'] / 100
            : 0;
        $calculatedSubtotal = 0;
        $totalSpecialDiscount = 0;
        $totalDiscountAmount = 0;
        $itemsData = [];
        $totalDiscount = 0;

        // Batch fetch MRPs for performance from product_variants table
        $productIds = array_filter(array_column($validatedData['items'], 'product_id'));
        $variants = \App\Models\ProductVariant::whereIn('product_id', $productIds)
            ->select('product_id', 'mrp')
            ->get()
            ->groupBy('product_id')
            ->map(function ($group) {
                return $group->first()->mrp;
            });

        foreach ($validatedData['items'] as $itemInput) {
            // Fetch the correct variant for this item
            $variant = null;
            if (!empty($itemInput['variant_id'])) {
                $variant = \App\Models\ProductVariant::find($itemInput['variant_id']);
            }
            // Use the variant's MRP if available, otherwise fallback to unit_price
            $mrp = $variant ? $variant->mrp : $itemInput['unit_price'];

            $itemSubtotal = $itemInput['qty'] * $mrp;
            $calculatedSubtotal += $itemSubtotal;
            $discountAmount = ($itemInput['discount_amount'] ?? 0);
            $specialDiscount = ($itemInput['special_discount'] ?? 0);
            $itemTotal = ($itemInput['qty'] * $itemInput['unit_price']) - $discountAmount;
            $totalDiscountAmount += $discountAmount;
            $totalDiscount += floatval($discountAmount) + floatval($specialDiscount);

            $buyingCost = $itemInput['total_buying_cost'] ?? 0;
            $profit = ($itemInput['sales_price'] - $buyingCost / ($itemInput['qty'] ?: 1)) * $itemInput['qty'];

            // Handle expiry date
            $expiryDate = null;
            if (!empty($itemInput['expiry_date']) && $itemInput['expiry_date'] !== 'N/A') {
                try {
                    $expiryDate = date('Y-m-d', strtotime($itemInput['expiry_date']));
                    if ($expiryDate === '1970-01-01' || !$expiryDate) {
                        $expiryDate = null;
                    }
                } catch (\Exception $e) {
                    $expiryDate = null;
                }
            }

            $itemsData[] = [
                'product_id' => $itemInput['product_id'] ?? null,
                'product_variant_id' => $itemInput['variant_id'] ?? null, // Batch tracking
                'batch_number' => $itemInput['batch_number'] ?? null, // Batch tracking
                'expiry_date' => $expiryDate, // Batch tracking
                'description' => $itemInput['description'],
                'quantity' => $itemInput['qty'],
                'free' => $itemInput['free'] ?? 0,
                'unit_price' => $itemInput['unit_price'],
                'sales_price' => $itemInput['sales_price'],
                'discount_amount' => $itemInput['discount_amount'],
                'discount_percentage' => $itemInput['discount_percentage'] ?? 0,
                'special_discount' => $itemInput['special_discount'] ?? 0,
                'total' => $itemTotal,
                'total_buying_cost' => $itemInput['total_buying_cost'] ?? 0,
                'profit' => $profit >= 0 ? $profit : 0,
                'supplier' => $itemInput['supplier'] ?? null,
                'category' => $itemInput['category'] ?? null,
                'store_location' => $itemInput['store_location'] ?? null,
            ];
        }

        $calculatedTaxAmount = $calculatedSubtotal * $taxRate;
        $calculatedTotalAmount = $calculatedSubtotal + $calculatedTaxAmount - $totalDiscountAmount;
        $calculatedBalance = $totalPaid - $calculatedTotalAmount;

        // Before payment validation
        Log::info('Invoice Payment Validation Debug', [
            'totalPaid' => $totalPaid,
            'calculatedTotalAmount' => $calculatedTotalAmount,
            'purchase_method' => $validatedData['purchaseDetails']['method'] ?? null,
            'redeemPoints' => $redeemPoints,
            'amountPaid' => $validatedData['purchaseDetails']['amount'] ?? 0,
            'request_payload' => $input,
        ]);

        // For cheque, total paid must not be greater than grand total
        if (($validatedData['purchaseDetails']['method'] ?? null) === 'cheque') {
            if ($totalPaid > $calculatedTotalAmount) {
                return response()->json([
                    'message' => 'Total paid must not be greater than grand total for cheque payments.',
                    'errors' => ['purchaseAmount' => ['Total paid must not be greater than grand total for cheque payments.']],
                ], 422);
            }
        }
        // For other payment methods, total paid must be at least grand total
        $fullPaymentMethods = ['cash', 'card', 'online', 'bank_transfer'];
        if (in_array(($validatedData['purchaseDetails']['method'] ?? null), $fullPaymentMethods)) {
            if ($totalPaid < $calculatedTotalAmount) {
                return response()->json([
                    'message' => 'Total paid must be at least grand total for ' . $validatedData['purchaseDetails']['method'] . ' payments.',
                    'errors' => ['purchaseAmount' => ['Total paid must be at least grand total for ' . $validatedData['purchaseDetails']['method'] . ' payments.']],
                ], 422);
            }
        }
        // For credit, total paid must be less than grand total
        if (($validatedData['purchaseDetails']['method'] ?? null) === 'credit') {
            if ($totalPaid >= $calculatedTotalAmount) {
                return response()->json([
                    'message' => 'Total paid must be less than grand total for credit payments.',
                    'errors' => ['purchaseAmount' => ['Total paid must be less than grand total for credit payments.']],
                ], 422);
            }
        }

        DB::beginTransaction();

        try {
            $cheque_no = $validatedData['cheque_no'] ?? null;
            $bank_name = $validatedData['bank_name'] ?? null;
            $issue_date = $validatedData['issue_date'] ?? null;

            if ($totalPaid >= $calculatedTotalAmount) {
                $status = 'paid';
            } elseif ($totalPaid > 0) {
                $status = 'partial';
            } else {
                $status = 'pending';
            }

            // Get bank ledger name if bank account is selected
            $bankLedgerName = null;
            if (!empty($validatedData['bank'])) {
                $bankAccountInfo = $validatedData['bank'];
                $bankAccountParts = explode('-', $bankAccountInfo);
                
                if (count($bankAccountParts) === 2) {
                    $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
                    $bankAccountId = $bankAccountParts[1];
                    
                    if ($bankAccountType === 'staff_ledger') {
                        $staffLedger = \App\Models\StaffLedger::find($bankAccountId);
                        $bankLedgerName = $staffLedger ? $staffLedger->name : null;
                    } else {
                        $subGroup = \App\Models\AccountSubGroup::find($bankAccountId);
                        $bankLedgerName = $subGroup ? $subGroup->sub_group_name : null;
                    }
                }
            }

            $invoice = Invoice::create([
                'invoice_no' => $validatedData['invoice']['no'] ?? Invoice::generateInvoiceNumber(),
                'invoice_date' => $validatedData['invoice']['date'],
                'invoice_time' => $validatedData['invoice']['time'],
                'customer_id' => $validatedData['customer']['id'] ?? null,
                'customer_name' => $validatedData['customer']['name'],
                'customer_address' => $validatedData['customer']['address'] ?? null,
                'customer_phone' => $validatedData['customer']['phone'] ?? null,
                'customer_email' => $validatedData['customer']['email'] ?? null,
                'payment_method' => $validatedData['purchaseDetails']['method'],
                'purchase_amount' => $totalPaid,
                'subtotal' => $calculatedSubtotal,
                'tax_percentage' => $validatedData['purchaseDetails']['taxPercentage'] ?? null,
                'tax_amount' => $calculatedTaxAmount,
                'total_amount' => $calculatedTotalAmount,
                'balance' => $calculatedBalance,
                'status' => $status,
                'quotation_id' => $validatedData['quotation_id'] ?? null,
                'prepared_by' => $validatedData['prepared_by'] ?? null,
               'approved_by' => $approvedBy, // Fix: Convert "" to NULL
                'cheque_no' => $cheque_no,
                'bank_name' => $bank_name,
                'issue_date' => $issue_date,
                'bank' => $bankLedgerName,
            ]);

            // Loyalty points update logic
            if ($invoice->customer_id) {
                $customer = \App\Models\Customer::find($invoice->customer_id);
                if ($customer) {
                    $sales = \App\Models\Sale::where('customer_id', $customer->id)->get();
                    $invoices = \App\Models\Invoice::where('customer_id', $customer->id)->get();
                    $allSales = $sales->concat($invoices);
                    $cards = $customer->loyalty_cards ?? collect();
                    $pointsEarned = 0;
                    foreach ($cards as $card) {
                        $pointsEarned += (new \App\Http\Controllers\LoyaltyPointsController)->calculatePointsEarned($card, $allSales);
                    }
                    $customer->save();
                }
            }

            // After $invoice = Invoice::create([...]);
            if ($invoice->customer_id && $redeemPoints > 0) {
                $customer = \App\Models\Customer::find($invoice->customer_id);
                if ($customer) {
                    $userId = auth()->id() ?? null;
                    Log::info('LoyaltyRedemption user_id debug', [
                        'user_id' => $userId,
                        'customer_id' => $customer->id,
                        'invoice_id' => $invoice->id,
                        'redeemPoints' => $redeemPoints,
                    ]);
                    $customer->points_redeemed = floatval($customer->points_redeemed) + $redeemPoints;
                    $customer->save();
                    // Log the redemption event
                    if (class_exists('App\\Models\\LoyaltyRedemption')) {
                        \App\Models\LoyaltyRedemption::create([
                            'customer_id' => $customer->id,
                            'invoice_id' => $invoice->id, // Use invoice_id for invoice redemptions
                            'redeemed_amount' => $redeemPoints,
                            'user_id' => $userId ?? null,
                            'notes' => 'Redeemed via Invoice',
                        ]);
                    }
                }
            }

            foreach ($itemsData as $itemData) {
                $invoice->items()->create($itemData);

                // Update stock quantity by subtracting sold quantity
                if ($itemData['product_id']) {
                    $product = \App\Models\Product::find($itemData['product_id']);
                    if ($product) {
                        $product->closing_stock_quantity = max(0, ($product->closing_stock_quantity ?? 0) - $itemData['quantity']);
                        $product->save();
                    }
                }
            }

            // Save payment details in payment_method table
            PaymentMethod::create([
                'type' => 'Invoice',
                'reference_number' => $invoice->invoice_no,
                'refer_type' => 'Customer',
                'refer_id' => $invoice->customer_id,
                'refer_name' => $invoice->customer_name ?? 'Walk-in Customer',
                'total' => $invoice->total_amount,
                'payment_type' => $invoice->payment_method ?? 'Unknown',
                'settled_amount' => $invoice->purchase_amount ?? 0,
                'balance_amount' => $invoice->balance ?? 0,
                'date' => $invoice->invoice_date->toDateString(),
                'cheque_no' => $cheque_no,
                'bank_name' => $bank_name,
                'issue_date' => $issue_date,
                'bank' => $bankLedgerName,
            ]);

            // Create bank account ledger entry for online, card, and cheque payments
            if (in_array(strtolower($invoice->payment_method), ['online', 'card', 'cheque']) &&
                !empty($validatedData['bank']) &&
                ($invoice->purchase_amount ?? 0) > 0) {

                $bankAccountInfo = $validatedData['bank'];
                $bankAccountParts = explode('-', $bankAccountInfo);

                if (count($bankAccountParts) === 2) {
                    $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
                    $bankAccountId = $bankAccountParts[1];

                    // Create payment entry for bank account ledger
                    Payment::create([
                        'voucher_no' => $invoice->invoice_no,
                        'transaction_id' => $invoice->id,
                        'transaction_type' => 'invoice_bank',
                        'reference_no' => $invoice->invoice_no,
                        'refer_type' => $bankAccountType === 'staff_ledger' ? 'Ledger' : 'SubGroup',
                        'refer_id' => $bankAccountId,
                        'refer_name' => $bankAccountType === 'staff_ledger' ?
                            (\App\Models\StaffLedger::find($bankAccountId)->name ?? 'Unknown Bank Account') :
                            (\App\Models\AccountSubGroup::find($bankAccountId)->sub_group_name ?? 'Unknown Bank Account'),
                        'amount' => $invoice->purchase_amount,
                        'discount' => 0,
                        'payment_date' => $invoice->invoice_date->toDateString(),
                        'payment_method' => ucfirst($invoice->payment_method),
                        'bank' => $bankLedgerName,
                        'note' => 'Sales invoice payment - Bank account debit entry',
                        'account_type' => 'Bank Accounts',
                    ]);
                }
            }

            // Cheque payment logic (store)
            if (strtolower($invoice->payment_method) === 'cheque' && ($invoice->purchase_amount ?? 0) > 0) {
                $payment = Payment::create([
                    'voucher_no' => $invoice->invoice_no,
                    'transaction_id' => $invoice->id,
                    'transaction_type' => 'invoice',
                    'reference_no' => $invoice->invoice_no,
                    'refer_type' => 'Customer',
                    'refer_id' => $invoice->customer_id,
                    'refer_name' => $invoice->customer_name ?? 'Walk-in Customer',
                    'amount' => $invoice->purchase_amount,
                    'discount' => 0, // Always provide discount
                    'payment_date' => $invoice->invoice_date->toDateString(),
                    'payment_method' => 'Cheque',
                    'cheque_no' => $cheque_no,
                    'bank_name' => $bank_name,
                    'issue_date' => $issue_date,
                    'note' => 'Invoice payment via cheque',
                    'discount' => '0',
                ]);
                ChequeStatement::create([
                    'payment_id' => $payment->id,
                    'voucher_no' => $payment->voucher_no,
                    'transaction_id' => $payment->transaction_id,
                    'transaction_type' => $payment->transaction_type,
                    'reference_no' => $payment->reference_no,
                    'refer_type' => $payment->refer_type,
                    'refer_id' => $payment->refer_id,
                    'refer_name' => $payment->refer_name,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date,
                    'cheque_no' => $payment->cheque_no,
                    'bank_name' => $payment->bank_name,
                    'issue_date' => $payment->issue_date,
                    'note' => $payment->note,
                    'status' => 'pending',
                ]);
            } else {
                // If payment type is not cheque, delete any existing cheque statement
                ChequeStatement::where('voucher_no', $invoice->invoice_no)->delete();
            }

            DB::commit();
            
            // Log user activity
            \App\Http\Controllers\UserActivityController::logActivity(
                auth()->id(),
                'Invoice Created',
                'Sales',
                $invoice->id,
                [
                    'invoice_no' => $invoice->invoice_no,
                    'customer_name' => $invoice->customer_name,
                    'total_amount' => $invoice->total_amount,
                    'payment_method' => $invoice->payment_method
                ]
            );

            $invoice->load('items');

            return response()->json([
                'message' => 'Invoice created successfully!',
                'invoice' => $invoice,
                'discount' => $totalDiscount ?? 0,
            ], 201);

        } catch (Throwable $e) {
            DB::rollBack();
            Log::error('Failed to create invoice:', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            return response()->json([
                'message' => 'Failed to create invoice: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function show(Invoice $invoice): JsonResponse
    {
        $invoice->load(['items.productVariant', 'deletedByUser', 'approvedByUser']);
        $data = $invoice->toArray();
        $data['paid_amount'] = $invoice->purchase_amount;
        $data['balance_amount'] = $invoice->balance;
        $data['bill_time'] = $invoice->invoice_time ?? ($invoice->invoice_date ? $invoice->invoice_date->format('h:i:s A') : null);
        return response()->json($data);
    }

    public function update(Invoice $invoice): JsonResponse
    {
        Log::info('Update Invoice Request Data:', request()->all());

        $validator = Validator::make(request()->all(), [
            'invoice.no' => 'nullable|string|max:255|unique:invoices,invoice_no,' . $invoice->id,
            'invoice.date' => 'required|date',
            'invoice.time' => 'required|date_format:H:i',
            'customer.id' => 'nullable|exists:customers,id',
            'customer.name' => 'required|string|max:255',
            'customer.address' => 'nullable|string|max:255',
            'customer.phone' => 'nullable|string|max:20',
            'customer.email' => 'nullable|email|max:255',
            'purchaseDetails.method' => 'required|string|in:cash,card,bank_transfer,cheque,online,credit',
            'purchaseDetails.amount' => 'required|numeric|min:0',
            'purchaseDetails.taxPercentage' => 'nullable|numeric|min:0|max:100',
            'items' => 'required|array|min:1',
            'items.*.id' => 'nullable|integer',
            'items.*.product_id' => 'nullable|exists:products,product_id',
            'items.*.description' => 'required|string|max:255',
            'items.*.qty' => 'required|numeric|min:0.01',
            'items.*.free' => 'nullable|numeric|min:0',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.sales_price' => 'required|numeric|min:0',
            'items.*.discount_amount' => 'required|numeric|min:0',
            'items.*.discount_percentage' => 'nullable|numeric|min:0|max:100',
            'items.*.special_discount' => 'required|numeric|min:0',
            'items.*.total_buying_cost' => 'required|numeric|min:0',
            'items.*.profit' => 'nullable|numeric|min:0',
            'status' => 'nullable|string|in:pending,paid,cancelled,unpaid,approved,rejected,partial',
            'items.*.supplier' => 'nullable|string|max:255',
            'items.*.category' => 'nullable|string|max:255',
            'items.*.store_location' => 'nullable|string|max:255',
            'quotation_id' => 'nullable|exists:quotations,id',
            'prepared_by' => 'nullable|string|max:255',
            'approved_by' => 'nullable|string|max:255',
            'cheque_no' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'issue_date' => 'nullable|date',
        ]);
        $input = request()->all();
        if (($input['purchaseDetails']['method'] ?? null) === 'cheque') {
            $validator->addRules([
                'cheque_no' => 'required|string|max:255',
                'bank_name' => 'required|string|max:255',
                'issue_date' => 'required|date',
            ]);
        }
        if ($validator->fails()) {
            Log::warning('Validation failed for invoice update:', [
                'errors' => $validator->errors(),
                'request_data' => request()->all(),
                'invoice_id' => $invoice->id,
            ]);
            return response()->json([
                'message' => 'Validation failed.',
                'errors' => $validator->errors(),
            ], 422);
        }

        $validatedData = $validator->validated();
        Log::info('Validated Update Invoice Data:', [
            'approved_by' => $validatedData['approved_by'] ?? 'not set',
            'full_data' => $validatedData,
        ]);

        // Save approved_by as is or null if not set, no default
        $approvedBy = $validatedData['approved_by'] ?? null;

        DB::beginTransaction();
        try {
            $cheque_no = $validatedData['cheque_no'] ?? null;
            $bank_name = $validatedData['bank_name'] ?? null;
            $issue_date = $validatedData['issue_date'] ?? null;
            $taxRate = isset($validatedData['purchaseDetails']['taxPercentage'])
                ? $validatedData['purchaseDetails']['taxPercentage'] / 100
                : 0;
            $calculatedSubtotal = 0;
            $totalDiscountAmount = 0;
            $itemsData = [];

        $productIds = array_filter(array_column($validatedData['items'], 'product_id'));
        $variants = \App\Models\ProductVariant::whereIn('product_id', $productIds)
            ->select('product_id', 'mrp')
            ->get()
            ->groupBy('product_id')
            ->map(function ($group) {
                return $group->first()->mrp;
            });

        foreach ($validatedData['items'] as $itemInput) {
            $mrp = $itemInput['product_id'] ? ($variants[$itemInput['product_id']] ?? $itemInput['unit_price']) : $itemInput['unit_price'];
            $itemSubtotal = $itemInput['qty'] * $mrp;
            $calculatedSubtotal += $itemSubtotal;
            $discountAmount = ($itemInput['discount_amount'] ?? 0);
            $itemTotal = ($itemInput['qty'] * $itemInput['sales_price']) - $discountAmount;
            $totalDiscountAmount += $discountAmount;

            $buyingCost = $itemInput['total_buying_cost'] ?? 0;
            $profit = ($itemInput['unit_price'] - $buyingCost / ($itemInput['qty'] ?: 1)) * $itemInput['qty'];

            $itemsData[] = [
                'id' => $itemInput['id'] ?? null,
                'product_id' => $itemInput['product_id'] ?? null,
                'description' => $itemInput['description'],
                'quantity' => $itemInput['qty'],
                'free' => $itemInput['free'] ?? 0,
                'unit_price' => $itemInput['unit_price'],
                'sales_price' => $itemInput['sales_price'],
                'discount_amount' => $itemInput['discount_amount'],
                'discount_percentage' => $itemInput['discount_percentage'] ?? 0,
                'special_discount' => $itemInput['special_discount'] ?? 0,
                'total' => $itemTotal,
                'total_buying_cost' => $itemInput['total_buying_cost'] ?? 0,
                'profit' => $profit >= 0 ? $profit : 0,
                'supplier' => $itemInput['supplier'] ?? null,
                'category' => $itemInput['category'] ?? null,
                'store_location' => $itemInput['store_location'] ?? null,
            ];
        }

            $calculatedTaxAmount = $calculatedSubtotal * $taxRate;
            $calculatedTotalAmount = $calculatedSubtotal + $calculatedTaxAmount - $totalDiscountAmount;
            $calculatedBalance = ($validatedData['purchaseDetails']['amount'] ?? 0) - $calculatedTotalAmount;
            if (($validatedData['purchaseDetails']['method'] ?? null) === 'cheque') {
                if (($validatedData['purchaseDetails']['amount'] ?? 0) > $calculatedTotalAmount) {
                    return response()->json([
                        'message' => 'Amount paid must not be greater than grand total for cheque payments.',
                        'errors' => ['purchaseAmount' => ['Amount paid must not be greater than grand total for cheque payments.']],
                    ], 422);
                }
            }

            $invoice->update([
                'invoice_no' => $validatedData['invoice']['no'] ?? $invoice->invoice_no,
                'invoice_date' => $validatedData['invoice']['date'],
                'invoice_time' => $validatedData['invoice']['time'],
                'customer_id' => $validatedData['customer']['id'] ?? $invoice->customer_id,
                'customer_name' => $validatedData['customer']['name'],
                'customer_address' => $validatedData['customer']['address'] ?? $invoice->customer_address,
                'customer_phone' => $validatedData['customer']['phone'] ?? $invoice->customer_phone,
                'customer_email' => $validatedData['customer']['email'] ?? $invoice->customer_email,
                'payment_method' => $validatedData['purchaseDetails']['method'],
                'purchase_amount' => $validatedData['purchaseDetails']['amount'],
                'subtotal' => $calculatedSubtotal,
                'tax_percentage' => $validatedData['purchaseDetails']['taxPercentage'] ?? $invoice->tax_percentage,
                'tax_amount' => $calculatedTaxAmount,
                'total_amount' => $calculatedTotalAmount,
                'balance' => $calculatedBalance,
                'status' => $validatedData['status'] ?? $invoice->status,
                'quotation_id' => $validatedData['quotation_id'] ?? $invoice->quotation_id,
                'prepared_by' => $validatedData['prepared_by'] ?? $invoice->prepared_by,
                'approved_by' => $approvedBy, // Fix: Convert "" to NULL
                'cheque_no' => $cheque_no,
                'bank_name' => $bank_name,
                'issue_date' => $issue_date,
            ]);

            $newItemIds = [];
            foreach ($itemsData as $index => $itemData) {
                $itemId = $itemData['id'] ?? null;

                // Debug logging
                Log::info("Processing item {$index}:", [
                    'item_id' => $itemId,
                    'product_id' => $itemData['product_id'] ?? null,
                    'description' => $itemData['description'] ?? null,
                    'quantity' => $itemData['quantity'] ?? null,
                ]);

                if ($itemId && $existingItem = $invoice->items()->find($itemId)) {
                    Log::info("Updating existing item {$itemId}");
                    $existingItem->update($itemData);
                    $newItemIds[] = $itemId;
                    // Update stock for existing item
                    if ($itemData['product_id']) {
                        $product = Product::find($itemData['product_id']);
                        if ($product) {
                            // Adjust stock by comparing old and new quantities
                            $oldQty = $existingItem->quantity;
                            $qtyDiff = $itemData['quantity'] - $oldQty;
                            $product->closing_stock_quantity = max(0, ($product->closing_stock_quantity ?? 0) - $qtyDiff);
                            $product->save();
                        }
                    }
                } else {
                    Log::info("Creating new item (ID: {$itemId} not found or null)");
                    $newItem = $invoice->items()->create($itemData);
                    $newItemIds[] = $newItem->id;
                    // Update stock for new item
                    if ($itemData['product_id']) {
                        $product = Product::find($itemData['product_id']);
                        if ($product) {
                            $product->closing_stock_quantity = max(0, ($product->closing_stock_quantity ?? 0) - $itemData['quantity']);
                            $product->save();
                        }
                    }
                }
            }

            // Delete items not included in the update
            $existingItemIds = $invoice->items->pluck('id')->toArray();
            $itemsToDelete = array_diff($existingItemIds, $newItemIds);
            if ($itemsToDelete) {
                $items = $invoice->items()->whereIn('id', $itemsToDelete)->get();
                foreach ($items as $item) {
                    if ($item->product_id) {
                        $product = Product::find($item->product_id);
                        if ($product) {
                            // Restore stock for deleted items
                            $product->closing_stock_quantity += $item->quantity;
                            $product->save();
                        }
                    }
                }
                $invoice->items()->whereIn('id', $itemsToDelete)->delete();
            }

            // Update payment details in payment_method table
            PaymentMethod::updateOrCreate(
                [
                    'type' => 'Invoice',
                    'reference_number' => $invoice->invoice_no,
                ],
                [
                    'refer_type' => 'Customer',
                    'refer_id' => $invoice->customer_id,
                    'refer_name' => $invoice->customer_name ?? 'Walk-in Customer',
                    'total' => $invoice->total_amount,
                    'payment_type' => $invoice->payment_method ?? 'Unknown',
                    'settled_amount' => $invoice->purchase_amount ?? 0,
                    'balance_amount' => $invoice->balance ?? 0,
                    'date' => $invoice->invoice_date->toDateString(),
                    'cheque_no' => $cheque_no,
                    'bank_name' => $bank_name,
                    'issue_date' => $issue_date,
                    'bank' => $validatedData['bank'] ?? null,
                ]
            );

            // Delete existing bank account ledger entries for this invoice
            Payment::where('transaction_type', 'invoice_bank')
                ->where('transaction_id', $invoice->id)
                ->delete();

            // Create bank account ledger entry for online, card, and cheque payments (update)
            if (in_array(strtolower($invoice->payment_method), ['online', 'card', 'cheque']) &&
                !empty($validatedData['bank']) &&
                ($invoice->purchase_amount ?? 0) > 0) {

                $bankAccountInfo = $validatedData['bank'];
                $bankAccountParts = explode('-', $bankAccountInfo);

                if (count($bankAccountParts) === 2) {
                    $bankAccountType = $bankAccountParts[0]; // 'staff_ledger' or 'sub_group'
                    $bankAccountId = $bankAccountParts[1];

                    // Create payment entry for bank account ledger
                    Payment::create([
                        'voucher_no' => $invoice->invoice_no,
                        'transaction_id' => $invoice->id,
                        'transaction_type' => 'invoice_bank',
                        'reference_no' => $invoice->invoice_no,
                        'refer_type' => $bankAccountType === 'staff_ledger' ? 'Ledger' : 'SubGroup',
                        'refer_id' => $bankAccountId,
                        'refer_name' => $bankAccountType === 'staff_ledger' ?
                            (\App\Models\StaffLedger::find($bankAccountId)->name ?? 'Unknown Bank Account') :
                            (\App\Models\AccountSubGroup::find($bankAccountId)->sub_group_name ?? 'Unknown Bank Account'),
                        'amount' => $invoice->purchase_amount,
                        'discount' => 0,
                        'payment_date' => $invoice->invoice_date->toDateString(),
                        'payment_method' => ucfirst($invoice->payment_method),
                        'bank' => $bankLedgerName,
                        'note' => 'Sales invoice payment - Bank account debit entry (updated)',
                        'account_type' => 'Bank Accounts',
                    ]);
                }
            }

            // Cheque payment logic (update)
            if (strtolower($invoice->payment_method) === 'cheque' && ($invoice->purchase_amount ?? 0) > 0) {
                // Delete existing cheque statement for this invoice if any
                ChequeStatement::where('voucher_no', $invoice->invoice_no)->delete();
                $payment = Payment::create([
                    'voucher_no' => $invoice->invoice_no,
                    'transaction_id' => $invoice->id,
                    'transaction_type' => 'invoice',
                    'reference_no' => $invoice->invoice_no,
                    'refer_type' => 'Customer',
                    'refer_id' => $invoice->customer_id,
                    'refer_name' => $invoice->customer_name ?? 'Walk-in Customer',
                    'amount' => $invoice->purchase_amount,
                    'discount' => 0, // Always provide discount
                    'payment_date' => $invoice->invoice_date->toDateString(),
                    'payment_method' => 'Cheque',
                    'cheque_no' => $cheque_no,
                    'bank_name' => $bank_name,
                    'issue_date' => $issue_date,
                    'note' => 'Invoice payment via cheque',
                ]);
                ChequeStatement::create([
                    'payment_id' => $payment->id,
                    'voucher_no' => $payment->voucher_no,
                    'transaction_id' => $payment->transaction_id,
                    'transaction_type' => $payment->transaction_type,
                    'reference_no' => $payment->reference_no,
                    'refer_type' => $payment->refer_type,
                    'refer_id' => $payment->refer_id,
                    'refer_name' => $payment->refer_name,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date,
                    'cheque_no' => $payment->cheque_no,
                    'bank_name' => $payment->bank_name,
                    'issue_date' => $payment->issue_date,
                    'note' => $payment->note,
                    'status' => 'pending',
                ]);
            } else {
                // If payment type is not cheque, delete any existing cheque statement
                ChequeStatement::where('voucher_no', $invoice->invoice_no)->delete();
            }

            DB::commit();
            $invoice->load('items');

            return response()->json([
                'message' => 'Invoice updated successfully!',
                'invoice' => $invoice,
                'discount' => $totalDiscount ?? 0,
            ], 200);

        } catch (Throwable $e) {
            DB::rollBack();
            Log::error('Failed to update invoice:', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_data' => request()->all(),
                'invoice_id' => $invoice->id,
            ]);
            return response()->json([
                'message' => 'Failed to update invoice.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function destroy(Request $request, $id)
    {
        Log::info('Deleting invoice', [
            'user_id' => $request->user()->id ?? auth()->id(),
            'invoice_id' => $id
        ]);
        $invoice = Invoice::with('items')->findOrFail($id);
        // Accept deleted_by from request, fallback to authenticated user
        $deletedBy = $request->input('deleted_by') ?? $request->user()->id ?? auth()->id();
        if (!$deletedBy) {
            Log::warning('No user ID found when deleting invoice', [
                'request_user' => $request->user(),
                'auth_id' => auth()->id(),
            ]);
            return response()->json(['message' => 'No authenticated or provided user found for delete action.'], 403);
        }
        // Set deleted_by for all items
        foreach ($invoice->items as $item) {
            $item->update(['deleted_by' => $deletedBy]);
            $item->delete();
        }
        // Set deleted_by for the invoice
        $invoice->update(['deleted_by' => $deletedBy]);
        $invoice->delete();

        // Log the delete activity
        \App\Http\Controllers\UserActivityController::logActivity(
            $deletedBy,
            'Invoice Deleted',
            'Sales',
            $invoice->id,
            [
                'invoice_no' => $invoice->invoice_no,
                'customer_name' => $invoice->customer_name,
                'total_amount' => $invoice->total_amount ?? 0,
                'deleted_by' => $deletedBy
            ]
        );

        return response()->json(['message' => 'Invoice and all items moved to trash'], 200);
    }

    public function getBillWiseProfitReport(Request $request)
    {
        try {
            Log::info('getBillWiseProfitReport called with paymentMethod', ['paymentMethod' => $request->input('paymentMethod')]);
            $query = Invoice::with('items')
                ->select('id', 'invoice_no', 'invoice_date', 'customer_name', 'payment_method');

            if ($request->has('fromDate') && $request->has('toDate')) {
                $query->whereBetween('invoice_date', [
                    $request->input('fromDate') . ' 00:00:00',
                    $request->input('toDate') . ' 23:59:59'
                ]);
            }

            if ($request->has('paymentMethod') && $request->input('paymentMethod') !== '' && $request->input('paymentMethod') !== 'all') {
                $query->where('payment_method', $request->input('paymentMethod'));
            }

            Log::info('Generated SQL query', ['sql' => $query->toSql()]);

            $invoices = $query->get();

            $reportData = [];
            $totalCostPriceAll = 0;
            $totalSellingPriceAll = 0;
            $totalProfitAll = 0;

            foreach ($invoices as $invoice) {
                $totalCostPrice = 0;
                $totalSellingPrice = 0;
                $items = [];

                foreach ($invoice->items as $item) {
                    $costPrice = $item->total_buying_cost ?? 0;
                    $sellingPrice = $item->sales_price * $item->quantity;
                    $profit = $sellingPrice - $costPrice;
                    $profitPercentage = ($sellingPrice > 0) ? ($profit / $sellingPrice) * 100 : 0;

                    $items[] = [
                        'product_name' => $item->description,
                        'quantity' => $item->quantity,
                        'costPrice' => number_format($costPrice, 2),
                        'sellingPrice' => number_format($sellingPrice, 2),
                        'profit' => number_format($profit, 2),
                        'profitPercentage' => number_format($profitPercentage, 2) . '%',
                    ];

                    $totalCostPrice += $costPrice;
                    $totalSellingPrice += $sellingPrice;
                }

                $totalProfit = $totalSellingPrice - $totalCostPrice;
                $profitPercentage = ($totalSellingPrice > 0) ? ($totalProfit / $totalSellingPrice) * 100 : 0;

                $reportData[] = [
                    'bill_number' => $invoice->invoice_no,
                    'date' => $invoice->invoice_date->format('d-m-Y'),
                    'customer_name' => $invoice->customer_name ?: 'Walk-in Customer',
                    'payment_type' => $invoice->payment_method,
                    'items' => $items,
                    'totalCostPrice' => number_format($totalCostPrice, 2),
                    'totalSellingPrice' => number_format($totalSellingPrice, 2),
                    'totalProfit' => number_format($totalProfit, 2),
                    'profitPercentage' => number_format($profitPercentage, 2) . '%',
                ];

                $totalCostPriceAll += $totalCostPrice;
                $totalSellingPriceAll += $totalSellingPrice;
                $totalProfitAll += $totalProfit;
            }

            $summary = [
                'totalCostPriceAll' => number_format($totalCostPriceAll, 2),
                'totalSellingPriceAll' => number_format($totalSellingPriceAll, 2),
                'totalProfitAll' => number_format($totalProfitAll, 2),
                'averageProfitPercentageAll' => ($totalSellingPriceAll > 0) ? number_format(($totalProfitAll / $totalSellingPriceAll) * 100, 2) . '%' : '0.00%',
            ];

            return response()->json([
                'reportData' => $reportData,
                'summary' => $summary,
            ]);
        } catch (Throwable $e) {
            Log::error('Error in getBillWiseProfitReport (Invoice): ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch invoice report.'], 500);
        }
    }

    public function approve($id)
    {
        $invoice = Invoice::find($id);
        if (!$invoice) {
            return response()->json(['message' => 'Invoice not found.'], 404);
        }

        $approvedBy = request()->input('approved_by');
        if (!$approvedBy) {
            return response()->json(['message' => 'approved_by is required.'], 400);
        }

        $invoice->approved_by = $approvedBy;
        $invoice->status = 'approved';
        $invoice->save();

        return response()->json([
            'message' => 'Invoice approved successfully.',
            'invoice' => $invoice,
        ]);
    }

    public function reject($id)
    {
        $invoice = Invoice::find($id);
        if (!$invoice) {
            return response()->json(['message' => 'Invoice not found.'], 404);
        }

        $rejectedBy = request()->input('rejected_by');
        if (!$rejectedBy) {
            return response()->json(['message' => 'rejected_by is required.'], 400);
        }

        // Preserve existing prepared_by and subtotal to avoid DB errors
        $preparedBy = $invoice->prepared_by;
        $subtotal = $invoice->subtotal;

        $invoice->rejected_by = $rejectedBy;
        $invoice->status = 'rejected';
        $invoice->prepared_by = $preparedBy;
        $invoice->subtotal = $subtotal;
        $invoice->save();

        return response()->json([
            'message' => 'Invoice rejected successfully.',
            'invoice' => $invoice,
        ]);
    }

    public function getDeletedInvoices()
    {
        $invoices = Invoice::onlyTrashed()->with(['items', 'customer', 'deletedByUser'])->paginate();
        return response()->json([
            'data' => collect($invoices->items())->map(function($invoice) {
                return array_merge($invoice->toArray(), [
                    'deleted_by_user' => $invoice->deletedByUser ? [
                        'id' => $invoice->deletedByUser->id,
                        'name' => $invoice->deletedByUser->name,
                        'email' => $invoice->deletedByUser->email,
                    ] : null,
                ]);
            }),
            'meta' => [
                'current_page' => $invoices->currentPage(),
                'last_page' => $invoices->lastPage(),
                'per_page' => $invoices->perPage(),
                'total' => $invoices->total(),
            ]
        ]);
    }

    public function restoreInvoice($id)
    {
        $invoice = Invoice::onlyTrashed()->findOrFail($id);
        $invoice->restore();
        // Restore related invoice items
        $invoice->items()->withTrashed()->restore();
        
        // Log the restore activity
        \App\Http\Controllers\UserActivityController::logActivity(
            auth()->id() ?? request()->user()->id ?? 1,
            'Invoice Restored',
            'Sales',
            $invoice->id,
            [
                'invoice_no' => $invoice->invoice_no,
                'customer_name' => $invoice->customer_name,
                'total_amount' => $invoice->total_amount ?? 0,
                'restored_by' => auth()->id() ?? request()->user()->id ?? 1
            ]
        );
        
        return response()->json($invoice->load(['items', 'customer']));
    }

    public function forceDeleteInvoice($id)
    {
        $invoice = Invoice::onlyTrashed()->findOrFail($id);
        
        // Log the force delete activity
        \App\Http\Controllers\UserActivityController::logActivity(
            auth()->id() ?? request()->user()->id ?? 1,
            'Invoice Permanently Deleted',
            'Sales',
            $invoice->id,
            [
                'invoice_no' => $invoice->invoice_no,
                'customer_name' => $invoice->customer_name,
                'total_amount' => $invoice->total_amount ?? 0,
                'permanently_deleted_by' => auth()->id() ?? request()->user()->id ?? 1
            ]
        );
        
        $invoice->forceDelete();
        return response()->noContent();
    }

    public function getDeletedUsers()
    {
        $users = User::onlyTrashed()->with('roles')->paginate();
        return response()->json([
            'data' => $users->items(),
            'meta' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
            ]
        ]);
    }

    public function getInvoiceItems($id)
    {
        $items = \App\Models\InvoiceItem::withTrashed()
            ->where('invoice_id', $id)
            ->get();

        return response()->json(['items' => $items]);
    }
}
