<?php

namespace App\Http\Controllers;

use App\Models\StaffLedger;
use App\Models\Payment;
use App\Http\Controllers\ChequeStatementController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class StaffLedgerController extends Controller
{
    public function index(Request $request)
    {
        $query = StaffLedger::query();

        if ($request->has('filter_type') && $request->has('search_query')) {
            $query->where($request->filter_type, 'like', '%' . $request->search_query . '%');
        }

        $ledgers = $query->get();

        return response()->json([
            'success' => true,
            'data' => $ledgers,
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'staff_id' => 'nullable|string|max:50|unique:staff_ledger,staff_id',
            'name' => 'required|string|max:255',
            'account_group' => 'required|string|max:255',
            'mobile_no' => 'nullable|string|max:20',
            'whatsapp_no' => 'nullable|string|max:20',
            'telephone_no' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'opening_balance' => 'nullable|numeric|min:0',
            'address' => 'nullable|string',
            'nic' => 'nullable|string|max:50',
            'position' => 'nullable|string|max:255',
            'join_date' => 'nullable|date',
            'date' => 'nullable|date',
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->only([
            'staff_id', 'name', 'address', 'mobile_no', 'whatsapp_no', 'telephone_no',
            'nic', 'opening_balance', 'account_group', 'email', 'position',
            'join_date', 'date',
        ]);

        // Generate staff_id if not provided
        if (empty($data['staff_id'])) {
            $lastLedger = StaffLedger::orderBy('id', 'desc')->first();
            $nextId = $lastLedger ? $lastLedger->id + 1 : 1;
            $data['staff_id'] = 'STF-' . str_pad($nextId, 3, '0', STR_PAD_LEFT);
        }

        if ($request->hasFile('profile_picture')) {
            $path = $request->file('profile_picture')->store('profile_pictures', 'public');
            $data['profile_picture'] = $path;
        }

        $ledger = StaffLedger::create($data);

        // Log user activity for ledger creation
        $userId = auth()->id();
        if ($userId) {
            \App\Http\Controllers\UserActivityController::logActivity(
                $userId,
                'Ledger Created',
                'Ledgers',
                $ledger->id,
                [
                    'staff_id' => $ledger->staff_id,
                    'name' => $ledger->name,
                    'account_group' => $ledger->account_group,
                    'email' => $ledger->email,
                    'position' => $ledger->position,
                    'opening_balance' => $ledger->opening_balance
                ]
            );
        }

        return response()->json([
            'success' => true,
            'data' => $ledger,
            'message' => 'Ledger created successfully',
        ], 201);
    }

    public function show($id)
    {
        $ledger = StaffLedger::find($id);

        if (!$ledger) {
            return response()->json([
                'success' => false,
                'message' => 'Ledger not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $ledger,
        ]);
    }

    public function update(Request $request, $id)
    {
        $ledger = StaffLedger::find($id);

        if (!$ledger) {
            return response()->json([
                'success' => false,
                'message' => 'Ledger not found',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'staff_id' => 'nullable|string|max:50|unique:staff_ledger,staff_id,' . $id,
            'name' => 'required|string|max:255',
            'account_group' => 'required|string|max:255',
            'mobile_no' => 'nullable|string|max:20',
            'whatsapp_no' => 'nullable|string|max:20',
            'telephone_no' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'opening_balance' => 'nullable|numeric|min:0',
            'address' => 'nullable|string',
            'nic' => 'nullable|string|max:50',
            'position' => 'nullable|string|max:255',
            'join_date' => 'nullable|date',
            'date' => 'nullable|date',
            'profile_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $request->only([
            'staff_id', 'name', 'address', 'mobile_no', 'whatsapp_no', 'telephone_no',
            'nic', 'opening_balance', 'account_group', 'email', 'position',
            'join_date', 'date',
        ]);

        if ($request->hasFile('profile_picture')) {
            // Delete old profile picture if exists
            if ($ledger->profile_picture) {
                Storage::disk('public')->delete($ledger->profile_picture);
            }
            $path = $request->file('profile_picture')->store('profile_pictures', 'public');
            $data['profile_picture'] = $path;
        }

        // Store old values for activity logging
        $oldValues = [
            'staff_id' => $ledger->staff_id,
            'name' => $ledger->name,
            'account_group' => $ledger->account_group,
            'email' => $ledger->email,
            'position' => $ledger->position,
            'opening_balance' => $ledger->opening_balance
        ];

        $ledger->update($data);

        // Log user activity for ledger update
        $userId = auth()->id();
        if ($userId) {
            \App\Http\Controllers\UserActivityController::logActivity(
                $userId,
                'Ledger Updated',
                'Ledgers',
                $ledger->id,
                [
                    'staff_id' => $ledger->staff_id,
                    'name' => $ledger->name,
                    'account_group' => $ledger->account_group,
                    'email' => $ledger->email,
                    'position' => $ledger->position,
                    'opening_balance' => $ledger->opening_balance
                ],
                $oldValues,
                [
                    'staff_id' => $ledger->staff_id,
                    'name' => $ledger->name,
                    'account_group' => $ledger->account_group,
                    'email' => $ledger->email,
                    'position' => $ledger->position,
                    'opening_balance' => $ledger->opening_balance
                ]
            );
        }

        return response()->json([
            'success' => true,
            'data' => $ledger,
            'message' => 'Ledger updated successfully',
        ]);
    }

    public function destroy($id)
    {
        $ledger = StaffLedger::find($id);

        if (!$ledger) {
            return response()->json([
                'success' => false,
                'message' => 'Ledger not found',
            ], 404);
        }

        // Log user activity for ledger deletion before deleting
        $userId = auth()->id();
        if ($userId) {
            \App\Http\Controllers\UserActivityController::logActivity(
                $userId,
                'Ledger Deleted',
                'Ledgers',
                $ledger->id,
                [
                    'staff_id' => $ledger->staff_id,
                    'name' => $ledger->name,
                    'account_group' => $ledger->account_group,
                    'email' => $ledger->email,
                    'position' => $ledger->position,
                    'opening_balance' => $ledger->opening_balance
                ]
            );
        }

        if ($ledger->profile_picture) {
            Storage::disk('public')->delete($ledger->profile_picture);
        }

        $ledger->delete();

        return response()->json([
            'success' => true,
            'message' => 'Ledger deleted successfully',
        ]);
    }

    /**
     * Store payment voucher transaction for ledger
     */
    public function storePaymentVoucher(Request $request)
    {
        // Log the incoming request for debugging
        Log::info('Payment voucher request data:', $request->all());

        $validator = Validator::make($request->all(), [
            'staff_id' => 'required|exists:staff_ledger,id',
            'amount' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|string',
            'cheque_no' => 'nullable|string',
            'bank_name' => 'nullable|string',
            'bank' => 'nullable|string',
            'issue_date' => 'nullable|date',
            'note' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            Log::error('Payment voucher validation failed:', $validator->errors()->toArray());
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Get staff ledger details
        $staffLedger = StaffLedger::find($request->staff_id);
        if (!$staffLedger) {
            return response()->json([
                'success' => false,
                'message' => 'Staff ledger not found',
            ], 404);
        }

        // For ledger transactions, add discount to the total amount
        $totalAmount = $request->amount + ($request->discount ?? 0);

        // Generate voucher number
        $voucherNo = Payment::getNextPaymentVoucherNumber();

        // Create payment record
        $payment = Payment::create([
            'voucher_no' => $voucherNo,
            'transaction_id' => $staffLedger->id,
            'transaction_type' => 'ledger',
            'reference_no' => $staffLedger->staff_id,
            'refer_type' => 'Ledger',
            'refer_id' => $staffLedger->id,
            'refer_name' => $staffLedger->name,
            'amount' => $totalAmount,
            'discount' => $request->discount ?? 0,
            'payment_date' => $request->payment_date,
            'payment_method' => $request->payment_method,
            'cheque_no' => $request->cheque_no,
            'bank_name' => $request->bank_name,
            'bank' => $request->bank,
            'issue_date' => $request->issue_date,
            'account_type' => $staffLedger->account_group,
            'note' => $request->note,
        ]);

        // Log user activity for payment voucher creation
        $userId = auth()->id();
        if ($userId) {
            \App\Http\Controllers\UserActivityController::logActivity(
                $userId,
                'Payment Voucher Created',
                'Payment Vouchers',
                $payment->id,
                [
                    'voucher_no' => $payment->voucher_no,
                    'transaction_type' => $payment->transaction_type,
                    'amount' => $payment->amount,
                    'payment_method' => $payment->payment_method,
                    'ledger_id' => $staffLedger->id,
                    'ledger_name' => $staffLedger->name
                ]
            );
        } else {
            \Log::error('Cannot log Payment Voucher Created - no authenticated user found');
        }

        // Create cheque statement if payment method is cheque
        if ($payment->payment_method === 'Cheque' || $payment->payment_method === 'cheque') {
            ChequeStatementController::createFromPayment($payment);
        }

        return response()->json([
            'success' => true,
            'data' => $payment,
            'message' => 'Payment voucher created successfully',
        ]);
    }

    /**
     * Store receive voucher transaction for ledger
     */
    public function storeReceiveVoucher(Request $request)
    {
        // Log the incoming request for debugging
        Log::info('Receive voucher request data:', $request->all());

        $validator = Validator::make($request->all(), [
            'staff_id' => 'required|exists:staff_ledger,id',
            'amount' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|string',
            'cheque_no' => 'nullable|string',
            'bank_name' => 'nullable|string',
            'bank' => 'nullable|string',
            'issue_date' => 'nullable|date',
            'note' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            Log::error('Receive voucher validation failed:', $validator->errors()->toArray());
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Get staff ledger details
        $staffLedger = StaffLedger::find($request->staff_id);
        if (!$staffLedger) {
            return response()->json([
                'success' => false,
                'message' => 'Staff ledger not found',
            ], 404);
        }

        // For ledger transactions, add discount to the total amount
        $totalAmount = $request->amount + ($request->discount ?? 0);

        // Generate voucher number
        $voucherNo = Payment::getNextReceiveVoucherNumber();

        // Create payment record
        $payment = Payment::create([
            'voucher_no' => $voucherNo,
            'transaction_id' => $staffLedger->id,
            'transaction_type' => 'ledger',
            'reference_no' => $voucherNo,
            'refer_type' => 'Ledger',
            'refer_id' => $staffLedger->id,
            'refer_name' => $staffLedger->name,
            'amount' => $totalAmount,
            'discount' => $request->discount ?? 0,
            'payment_date' => $request->payment_date,
            'payment_method' => $request->payment_method,
            'cheque_no' => $request->cheque_no,
            'bank_name' => $request->bank_name,
            'bank' => $request->bank,
            'issue_date' => $request->issue_date,
            'account_type' => $staffLedger->account_group,
            'note' => $request->note,
        ]);

        // Log user activity for receive voucher creation
        $userId = auth()->id();
        if ($userId) {
            \App\Http\Controllers\UserActivityController::logActivity(
                $userId,
                'Receive Voucher Created',
                'Receive Vouchers',
                $payment->id,
                [
                    'voucher_no' => $payment->voucher_no,
                    'transaction_type' => $payment->transaction_type,
                    'amount' => $payment->amount,
                    'payment_method' => $payment->payment_method,
                    'ledger_id' => $staffLedger->id,
                    'ledger_name' => $staffLedger->name
                ]
            );
        } else {
            \Log::error('Cannot log Receive Voucher Created - no authenticated user found');
        }

        // Create cheque statement if payment method is cheque
        if ($payment->payment_method === 'Cheque' || $payment->payment_method === 'cheque') {
            ChequeStatementController::createFromPayment($payment);
        }

        return response()->json([
            'success' => true,
            'data' => $payment,
            'message' => 'Receive voucher created successfully',
        ]);
    }

    /**
     * Get ledger data for vouchers
     */
    public function getLedgerForVouchers(Request $request)
    {
        $query = StaffLedger::select('id', 'staff_id', 'name', 'account_group', 'opening_balance');

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('staff_id', 'like', "%{$search}%");
            });
        }

        $ledgers = $query->orderBy('name')->get();

        return response()->json([
            'success' => true,
            'data' => $ledgers,
        ]);
    }

    /**
     * Get next payment voucher number
     */
    public function getNextPaymentVoucherNumber()
    {
        try {
            $nextVoucherNumber = Payment::getNextPaymentVoucherNumber();

            return response()->json([
                'success' => true,
                'voucher_number' => $nextVoucherNumber,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate voucher number',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get next receive voucher number
     */
    public function getNextReceiveVoucherNumber()
    {
        try {
            $nextVoucherNumber = Payment::getNextReceiveVoucherNumber();

            return response()->json([
                'success' => true,
                'voucher_number' => $nextVoucherNumber,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate voucher number',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get bank accounts for payment methods
     */
    public function getBankAccounts()
    {
        try {
            // Get bank account sub groups from AccountSubGroup to know which sub groups exist
            $bankSubGroups = \App\Models\AccountSubGroup::where('main_group', 'Bank Accounts')
                ->pluck('sub_group_name')
                ->toArray();

            // Create array of all bank account groups (main group + sub groups)
            $bankAccountGroups = array_merge(['Bank Accounts'], $bankSubGroups);

            // Get bank accounts from staff ledger for main group and all sub groups
            $staffBankAccounts = StaffLedger::whereIn('account_group', $bankAccountGroups)
                ->select('id', 'name', 'account_group')
                ->get()
                ->map(function ($account) {
                    return [
                        'id' => $account->id,
                        'name' => $account->name,
                        'account_group' => $account->account_group,
                        'type' => 'staff_ledger'
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $staffBankAccounts,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching bank accounts: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch bank accounts',
            ], 500);
        }
    }

    /**
     * Get bank account subgroups for dropdown
     */
    public function getBankAccountSubgroups()
    {
        try {
            // Get bank account sub groups from AccountSubGroup to know which sub groups exist
            $bankSubGroups = \App\Models\AccountSubGroup::where('main_group', 'Bank Accounts')
                ->pluck('sub_group_name')
                ->toArray();

            // Create array of all bank account groups (main group + sub groups)
            $bankAccountGroups = array_merge(['Bank Accounts'], $bankSubGroups);

            // Get staff ledger entries for main group and all sub groups
            $staffBankAccounts = StaffLedger::whereIn('account_group', $bankAccountGroups)
                ->select('id', 'name', 'account_group', 'staff_id')
                ->get()
                ->map(function ($account) {
                    return [
                        'id' => $account->id,
                        'staff_id' => $account->staff_id,
                        'name' => $account->name,
                        'account_group' => $account->account_group,
                        'type' => 'staff_ledger'
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $staffBankAccounts,
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching bank account subgroups: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch bank account subgroups',
            ], 500);
        }
    }
}